// 团队游戏化系统核心组件
import React, { useState, useEffect } from 'react';
import './TeamGameification.css';

// 成就徽章系统
export const AchievementBadges = () => {
    const [userAchievements, setUserAchievements] = useState([]);
    const [showNewBadge, setShowNewBadge] = useState(false);

    const achievements = {
        "初来乍到": { 
            icon: "🌱", 
            description: "完成第一个任务",
            rarity: "common",
            poem: "千里之行始于足下"
        },
        "博览群书": { 
            icon: "📚", 
            description: "掌握5种新技术",
            rarity: "rare",
            poem: "博观而约取，厚积而薄发"
        },
        "代码诗人": { 
            icon: "🖋️", 
            description: "写出最佳注释",
            rarity: "epic",
            poem: "文以载道，代码传情"
        },
        "bug终结者": { 
            icon: "🔫", 
            description: "修复10个bug",
            rarity: "legendary",
            poem: "千淘万漉虽辛苦，吹尽狂沙始到金"
        }
    };

    const unlockAchievement = (achievementName) => {
        if (!userAchievements.includes(achievementName)) {
            setUserAchievements([...userAchievements, achievementName]);
            setShowNewBadge(achievementName);
            
            // 播放解锁音效
            playAchievementSound();
            
            // 3秒后隐藏新徽章提示
            setTimeout(() => setShowNewBadge(false), 3000);
        }
    };

    const playAchievementSound = () => {
        const audio = new Audio('/sounds/achievement-unlock.mp3');
        audio.play().catch(e => console.log('Audio play failed:', e));
    };

    return (
        <div className="achievement-system">
            <h3 className="achievement-title">📜 成就榜</h3>
            
            {/* 新徽章解锁动画 */}
            {showNewBadge && (
                <div className="new-badge-animation">
                    <div className="badge-container">
                        <div className="badge-glow">
                            <span className="badge-icon">{achievements[showNewBadge].icon}</span>
                        </div>
                        <div className="badge-text">
                            <h4>恭喜解锁新成就！</h4>
                            <p>{achievements[showNewBadge].description}</p>
                            <div className="badge-poem">"{achievements[showNewBadge].poem}"</div>
                        </div>
                    </div>
                    <div className="celebration-effects">
                        {[...Array(8)].map((_, i) => (
                            <div key={i} className={`confetti confetti-${i}`}>🎊</div>
                        ))}
                    </div>
                </div>
            )}

            {/* 徽章展示区 */}
            <div className="badges-grid">
                {Object.entries(achievements).map(([name, achievement]) => (
                    <div 
                        key={name}
                        className={`badge ${userAchievements.includes(name) ? 'unlocked' : 'locked'} ${achievement.rarity}`}
                        onClick={() => !userAchievements.includes(name) && unlockAchievement(name)}
                    >
                        <div className="badge-icon">{achievement.icon}</div>
                        <div className="badge-info">
                            <h4>{name}</h4>
                            <p>{achievement.description}</p>
                            {userAchievements.includes(name) && (
                                <div className="badge-poem">"{achievement.poem}"</div>
                            )}
                        </div>
                        {!userAchievements.includes(name) && (
                            <div className="badge-lock">🔒</div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

// Sprint主题切换组件
export const SprintThemeManager = () => {
    const [currentSprint, setCurrentSprint] = useState(1);
    
    const sprintThemes = {
        1: {
            name: "📖 开卷有益",
            color: "#FF8C42",
            bgGradient: "linear-gradient(135deg, #FF8C42, #FFA726)",
            symbol: "📚",
            greeting: "千里之行始于足下，万卷诗书始于开篇"
        },
        2: {
            name: "🔍 博览群书", 
            color: "#4A90E2",
            bgGradient: "linear-gradient(135deg, #4A90E2, #64B5F6)",
            symbol: "🔍",
            greeting: "博观而约取，厚积而薄发"
        },
        3: {
            name: "🎨 文以载道",
            color: "#8E44AD", 
            bgGradient: "linear-gradient(135deg, #8E44AD, #BA68C8)",
            symbol: "🎨",
            greeting: "文质彬彬，然后君子"
        },
        4: {
            name: "⚡ 学而时习",
            color: "#2ECC71",
            bgGradient: "linear-gradient(135deg, #2ECC71, #66BB6A)", 
            symbol: "⚡",
            greeting: "学而时习之，不亦说乎"
        },
        5: {
            name: "🤝 有朋自远方来",
            color: "#E74C3C",
            bgGradient: "linear-gradient(135deg, #E74C3C, #EF5350)",
            symbol: "🤝", 
            greeting: "有朋自远方来，不亦乐乎"
        },
        6: {
            name: "🧪 温故知新",
            color: "#95A5A6",
            bgGradient: "linear-gradient(135deg, #95A5A6, #B0BEC5)",
            symbol: "🧪",
            greeting: "温故而知新，可以为师矣"
        },
        7: {
            name: "🚀 厚积薄发", 
            color: "#F1C40F",
            bgGradient: "linear-gradient(135deg, #F1C40F, #FFEB3B)",
            symbol: "🚀",
            greeting: "厚积薄发，一鸣惊人"
        },
        8: {
            name: "🎓 毕业典礼",
            color: "#FFD700", 
            bgGradient: "linear-gradient(135deg, #FFD700, #FFC107)",
            symbol: "🎓",
            greeting: "书山有路勤为径，学海无涯苦作舟"
        }
    };

    const theme = sprintThemes[currentSprint];

    useEffect(() => {
        // 更新CSS变量以应用主题
        const root = document.documentElement;
        root.style.setProperty('--sprint-color', theme.color);
        root.style.setProperty('--sprint-gradient', theme.bgGradient);
    }, [currentSprint, theme]);

    const switchSprint = (sprintNumber) => {
        setCurrentSprint(sprintNumber);
        
        // 播放切换音效
        const audio = new Audio('/sounds/page-turn.mp3');
        audio.play().catch(e => console.log('Audio play failed:', e));
        
        // 显示主题切换动画
        showThemeTransition(sprintThemes[sprintNumber]);
    };

    const showThemeTransition = (newTheme) => {
        const transitionEl = document.createElement('div');
        transitionEl.className = 'theme-transition';
        transitionEl.innerHTML = `
            <div class="transition-content">
                <div class="theme-symbol">${newTheme.symbol}</div>
                <h2 class="theme-name">${newTheme.name}</h2>
                <p class="theme-greeting">${newTheme.greeting}</p>
            </div>
        `;
        document.body.appendChild(transitionEl);
        
        setTimeout(() => {
            transitionEl.remove();
        }, 2000);
    };

    return (
        <div className="sprint-theme-manager">
            <div className="current-theme" style={{background: theme.bgGradient}}>
                <div className="theme-header">
                    <span className="theme-symbol">{theme.symbol}</span>
                    <h2 className="theme-name">{theme.name}</h2>
                </div>
                <p className="theme-greeting">"{theme.greeting}"</p>
            </div>
            
            <div className="sprint-selector">
                <h4>选择Sprint主题</h4>
                <div className="sprint-buttons">
                    {Object.entries(sprintThemes).map(([number, sprint]) => (
                        <button
                            key={number}
                            className={`sprint-btn ${currentSprint == number ? 'active' : ''}`}
                            onClick={() => switchSprint(number)}
                            style={{borderColor: sprint.color}}
                        >
                            <span className="sprint-symbol">{sprint.symbol}</span>
                            <span className="sprint-number">Sprint {number}</span>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
};

// 每日"书香站会"组件
export const DailyStandup = () => {
    const [todayQuote, setTodayQuote] = useState('');
    const [teamMembers, setTeamMembers] = useState([]);
    
    const inspirationalQuotes = [
        "书山有路勤为径，学海无涯苦作舟",
        "读书破万卷，下笔如有神", 
        "博学之，审问之，慎思之，明辨之，笃行之",
        "学而时习之，不亦说乎",
        "温故而知新，可以为师矣",
        "三人行，必有我师焉",
        "知之者不如好之者，好之者不如乐之者",
        "敏而好学，不耻下问"
    ];

    useEffect(() => {
        // 每日随机选择一句名言
        const randomQuote = inspirationalQuotes[Math.floor(Math.random() * inspirationalQuotes.length)];
        setTodayQuote(randomQuote);
    }, []);

    const startStandup = () => {
        // 播放上课铃声
        const audio = new Audio('/sounds/class-bell.mp3');
        audio.play().catch(e => console.log('Audio play failed:', e));
        
        // 显示站会开始动画
        showStandupAnimation();
    };

    const showStandupAnimation = () => {
        const animationEl = document.createElement('div');
        animationEl.className = 'standup-animation';
        animationEl.innerHTML = `
            <div class="standup-content">
                <div class="bell-animation">🔔</div>
                <h3>书香站会开始</h3>
                <p>"${todayQuote}"</p>
            </div>
        `;
        document.body.appendChild(animationEl);
        
        setTimeout(() => {
            animationEl.remove();
        }, 3000);
    };

    return (
        <div className="daily-standup">
            <div className="standup-header">
                <h3>📚 今日书香站会</h3>
                <div className="quote-of-day">
                    <p>"{todayQuote}"</p>
                </div>
            </div>
            
            <button className="start-standup-btn" onClick={startStandup}>
                🔔 开始站会
            </button>
            
            <div className="standup-agenda">
                <h4>站会议程</h4>
                <ul>
                    <li>📖 今日书摘分享</li>
                    <li>💻 昨日完成工作</li>
                    <li>🎯 今日工作计划</li>
                    <li>🚧 遇到的障碍</li>
                    <li>🌟 今日学习收获</li>
                </ul>
            </div>
        </div>
    );
};

// Bug追踪"侦探游戏"组件
export const BugDetectiveGame = () => {
    const [activeCases, setActiveCases] = useState([]);
    const [solvedCases, setSolvedCases] = useState([]);
    
    const createBugCase = (bugInfo) => {
        const caseId = `CASE-${Date.now()}`;
        const newCase = {
            id: caseId,
            title: `${bugInfo.title}失踪案`,
            description: bugInfo.description,
            suspect: bugInfo.component,
            clues: bugInfo.clues || [],
            reward: bugInfo.priority === 'high' ? '🏆 金侦探徽章' : '🥉 铜侦探徽章',
            createdAt: new Date().toISOString(),
            status: 'investigating'
        };
        
        setActiveCases([...activeCases, newCase]);
        return caseId;
    };

    const solveCase = (caseId, solution) => {
        const caseToSolve = activeCases.find(c => c.id === caseId);
        if (caseToSolve) {
            const solvedCase = {
                ...caseToSolve,
                status: 'solved',
                solution: solution,
                solvedAt: new Date().toISOString()
            };
            
            setSolvedCases([...solvedCases, solvedCase]);
            setActiveCases(activeCases.filter(c => c.id !== caseId));
            
            // 播放破案音效
            const audio = new Audio('/sounds/case-solved.mp3');
            audio.play().catch(e => console.log('Audio play failed:', e));
            
            // 显示破案庆祝动画
            showCaseSolvedAnimation(solvedCase);
        }
    };

    const showCaseSolvedAnimation = (solvedCase) => {
        const animationEl = document.createElement('div');
        animationEl.className = 'case-solved-animation';
        animationEl.innerHTML = `
            <div class="solved-content">
                <div class="detective-badge">🔍</div>
                <h3>案件告破！</h3>
                <p>恭喜侦探破获"${solvedCase.title}"</p>
                <div class="reward">${solvedCase.reward}</div>
            </div>
        `;
        document.body.appendChild(animationEl);
        
        setTimeout(() => {
            animationEl.remove();
        }, 3000);
    };

    return (
        <div className="bug-detective-game">
            <div className="detective-header">
                <h3>🔍 Bug侦探局</h3>
                <div className="detective-stats">
                    <span>🔍 进行中: {activeCases.length}</span>
                    <span>✅ 已破获: {solvedCases.length}</span>
                </div>
            </div>
            
            <div className="active-cases">
                <h4>📋 当前案件</h4>
                {activeCases.map(bugCase => (
                    <div key={bugCase.id} className="case-card">
                        <div className="case-header">
                            <h5>🔍 {bugCase.title}</h5>
                            <span className="case-id">{bugCase.id}</span>
                        </div>
                        
                        <div className="case-details">
                            <p><strong>案件描述:</strong> {bugCase.description}</p>
                            <p><strong>嫌疑对象:</strong> {bugCase.suspect}</p>
                            <p><strong>悬赏奖励:</strong> {bugCase.reward}</p>
                        </div>
                        
                        <div className="case-actions">
                            <button 
                                className="solve-case-btn"
                                onClick={() => {
                                    const solution = prompt('请输入破案方案:');
                                    if (solution) solveCase(bugCase.id, solution);
                                }}
                            >
                                🎯 提交破案方案
                            </button>
                        </div>
                    </div>
                ))}
            </div>
            
            <div className="solved-cases">
                <h4>🏆 破案档案</h4>
                {solvedCases.slice(-3).map(solvedCase => (
                    <div key={solvedCase.id} className="solved-case-card">
                        <h5>✅ {solvedCase.title}</h5>
                        <p><strong>破案方案:</strong> {solvedCase.solution}</p>
                        <div className="case-reward">{solvedCase.reward}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default {
    AchievementBadges,
    SprintThemeManager, 
    DailyStandup,
    BugDetectiveGame
};
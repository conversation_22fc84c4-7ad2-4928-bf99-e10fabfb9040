-- 大学生收书卖书平台数据库结构设计
-- 创建时间: 2025-01-30
-- 版本: v1.0 - 核心表优先设计

-- 删除已存在的表（开发环境）
DROP TABLE IF EXISTS admin_logs CASCADE;
DROP TABLE IF EXISTS order_items CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS books CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 枚举类型定义
CREATE TYPE user_role AS ENUM ('user', 'admin', 'super_admin');
CREATE TYPE register_type AS ENUM ('phone', 'email', 'wechat', 'qq');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'banned');
CREATE TYPE book_condition AS ENUM ('new', 'like_new', 'good', 'fair');
CREATE TYPE book_status AS ENUM ('available', 'sold', 'reserved', 'inactive');
CREATE TYPE order_status AS ENUM ('pending', 'paid', 'delivering', 'delivered', 'cancelled', 'return_requested', 'returned');
CREATE TYPE payment_method AS ENUM ('wechat', 'alipay', 'offline');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE message_type AS ENUM ('chat', 'book_inquiry', 'order_inquiry', 'system_notification');
CREATE TYPE message_status AS ENUM ('active', 'hidden', 'deleted');
CREATE TYPE log_action AS ENUM ('create', 'update', 'delete', 'login', 'logout');
CREATE TYPE log_target AS ENUM ('book', 'order', 'user', 'message', 'system');

-- =============================================
-- 1. 用户表 (users) - 核心基础表
-- =============================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    avatar TEXT,
    register_type register_type NOT NULL DEFAULT 'phone',
    third_party_id VARCHAR(100), -- 第三方平台用户ID
    phone_verified BOOLEAN DEFAULT false,
    email_verified BOOLEAN DEFAULT false,
    status user_status DEFAULT 'active',
    created_by UUID REFERENCES users(id), -- 创建者ID（管理员账号）
    
    -- 管理员联系方式（仅管理员账号）
    contact_wechat VARCHAR(50),
    contact_qq VARCHAR(20),
    contact_phone_public VARCHAR(20),
    
    -- 时间戳
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- =============================================
-- 2. 分类表 (categories) - 图书分类
-- =============================================
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id), -- 支持层级分类
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分类表索引
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_active ON categories(is_active);

-- =============================================
-- 3. 图书表 (books) - 核心业务表
-- =============================================
CREATE TABLE books (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    isbn VARCHAR(20),
    title VARCHAR(255) NOT NULL,
    author VARCHAR(255) NOT NULL,
    publisher VARCHAR(255),
    publication_date DATE,
    category_id UUID REFERENCES categories(id),
    description TEXT,
    condition book_condition NOT NULL DEFAULT 'good',
    status book_status NOT NULL DEFAULT 'available',
    
    -- 价格和库存
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2), -- 原价（用于显示优惠）
    stock INTEGER NOT NULL DEFAULT 1,
    sold_count INTEGER DEFAULT 0, -- 销售数量
    
    -- 图片
    cover_image TEXT, -- 封面图片URL
    images TEXT[], -- 其他图片URL数组
    
    -- 管理信息
    created_by UUID REFERENCES users(id), -- 录入者（管理员）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 图书表索引
CREATE INDEX idx_books_isbn ON books(isbn);
CREATE INDEX idx_books_title ON books USING GIN(to_tsvector('chinese', title));
CREATE INDEX idx_books_author ON books USING GIN(to_tsvector('chinese', author));
CREATE INDEX idx_books_category_id ON books(category_id);
CREATE INDEX idx_books_status ON books(status);
CREATE INDEX idx_books_price ON books(price);
CREATE INDEX idx_books_created_at ON books(created_at);

-- =============================================
-- 4. 订单表 (orders) - 核心业务表
-- =============================================
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(32) UNIQUE NOT NULL, -- 订单号
    user_id UUID NOT NULL REFERENCES users(id),
    
    -- 订单金额
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_fee DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    
    -- 订单状态
    status order_status NOT NULL DEFAULT 'pending',
    
    -- 支付信息
    payment_method payment_method,
    payment_status payment_status DEFAULT 'pending',
    paid_at TIMESTAMP,
    
    -- 配送信息
    delivery_address TEXT NOT NULL,
    delivery_phone VARCHAR(20) NOT NULL,
    delivery_name VARCHAR(100) NOT NULL,
    delivery_notes TEXT,
    delivery_person VARCHAR(100), -- 配送员姓名
    delivery_person_phone VARCHAR(20), -- 配送员电话
    delivered_at TIMESTAMP,
    
    -- 退货信息
    return_reason TEXT,
    return_requested_at TIMESTAMP,
    return_approved_at TIMESTAMP,
    return_completed_at TIMESTAMP,
    
    -- 备注和管理
    notes TEXT, -- 订单备注
    admin_notes TEXT, -- 管理员备注（用户不可见）
    processed_by UUID REFERENCES users(id), -- 处理者（管理员）
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表索引
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- =============================================
-- 5. 订单详情表 (order_items)
-- =============================================
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES books(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    
    -- 冗余书籍信息（防止书籍信息变更影响历史订单）
    book_title VARCHAR(255) NOT NULL,
    book_author VARCHAR(255) NOT NULL,
    book_isbn VARCHAR(20),
    book_cover_image TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单详情表索引
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_book_id ON order_items(book_id);

-- =============================================
-- 6. 消息表 (messages) - 用户交流和咨询
-- =============================================
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID NOT NULL REFERENCES users(id),
    receiver_id UUID REFERENCES users(id), -- 接收者ID，系统消息时为空
    book_id UUID REFERENCES books(id), -- 关联图书ID（图书咨询）
    order_id UUID REFERENCES orders(id), -- 关联订单ID（订单咨询）
    
    content TEXT NOT NULL,
    type message_type NOT NULL DEFAULT 'chat',
    status message_status NOT NULL DEFAULT 'active',
    
    read_at TIMESTAMP, -- 已读时间
    is_admin_reply BOOLEAN DEFAULT false, -- 是否为管理员回复
    parent_message_id UUID REFERENCES messages(id), -- 父消息ID（回复功能）
    
    -- 管理信息
    reviewed_by UUID REFERENCES users(id), -- 审核者
    reviewed_at TIMESTAMP, -- 审核时间
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 消息表索引
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
CREATE INDEX idx_messages_book_id ON messages(book_id);
CREATE INDEX idx_messages_order_id ON messages(order_id);
CREATE INDEX idx_messages_type ON messages(type);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- =============================================
-- 7. 管理员操作日志表 (admin_logs)
-- =============================================
CREATE TABLE admin_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID NOT NULL REFERENCES users(id),
    action log_action NOT NULL,
    target_type log_target NOT NULL,
    target_id UUID, -- 操作对象ID
    description TEXT NOT NULL, -- 操作描述
    
    -- 请求信息
    ip_address INET,
    user_agent TEXT,
    request_data JSONB, -- 请求数据
    
    -- 操作结果
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 管理员日志索引
CREATE INDEX idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX idx_admin_logs_action ON admin_logs(action);
CREATE INDEX idx_admin_logs_target_type ON admin_logs(target_type);
CREATE INDEX idx_admin_logs_created_at ON admin_logs(created_at);

-- =============================================
-- 触发器函数 - 自动更新 updated_at
-- =============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加 updated_at 触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_books_updated_at BEFORE UPDATE ON books
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 订单号生成函数
-- =============================================
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_date TEXT;
    sequence_num TEXT;
BEGIN
    -- 生成格式：BK + YYYYMMDD + 6位序号
    order_date := TO_CHAR(CURRENT_DATE, 'YYYYMMDD');
    
    -- 获取当日订单序号
    SELECT LPAD((COUNT(*) + 1)::TEXT, 6, '0') INTO sequence_num
    FROM orders 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    RETURN 'BK' || order_date || sequence_num;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 初始化基础数据
-- =============================================

-- 创建超级管理员账号
INSERT INTO users (
    id, username, phone, email, password_hash, role, status, phone_verified
) VALUES (
    gen_random_uuid(),
    'admin',
    '13800000000',
    '<EMAIL>',
    '$2b$10$rOZjT8.PrOF3Q2YYYq3QHeK5HTcJO5F7oI.EjjGxH.ZZ5KK9L8Niq', -- 密码: admin123
    'super_admin',
    'active',
    true
) ON CONFLICT (phone) DO NOTHING;

-- 创建基础图书分类
INSERT INTO categories (name, description, sort_order) VALUES
    ('教材类', '大学教材、参考书', 1),
    ('文学类', '小说、散文、诗歌', 2),
    ('工具书', '词典、手册、指南', 3),
    ('考试类', '考研、四六级、证书考试', 4),
    ('计算机', '编程、软件开发', 5),
    ('其他', '其他类别图书', 6)
ON CONFLICT DO NOTHING;

-- 创建示例图书数据（用于测试）
WITH sample_category AS (
    SELECT id FROM categories WHERE name = '教材类' LIMIT 1
)
INSERT INTO books (
    title, author, publisher, category_id, description, 
    price, original_price, stock, condition, status
) SELECT 
    '高等数学（上册）', '同济大学数学系', '高等教育出版社', 
    sample_category.id, '经典高等数学教材，适合大学一年级使用',
    45.50, 89.00, 10, 'good', 'available'
FROM sample_category
ON CONFLICT DO NOTHING;

-- =============================================
-- 数据库统计视图
-- =============================================

-- 用户统计视图
CREATE VIEW user_stats AS
SELECT 
    role,
    status,
    COUNT(*) as count,
    DATE(created_at) as date
FROM users 
GROUP BY role, status, DATE(created_at);

-- 图书统计视图
CREATE VIEW book_stats AS
SELECT 
    c.name as category_name,
    b.status,
    COUNT(*) as count,
    AVG(b.price) as avg_price,
    SUM(b.stock) as total_stock
FROM books b
LEFT JOIN categories c ON b.category_id = c.id
GROUP BY c.name, b.status;

-- 订单统计视图
CREATE VIEW order_stats AS
SELECT 
    status,
    payment_status,
    COUNT(*) as count,
    SUM(final_amount) as total_amount,
    DATE(created_at) as date
FROM orders
GROUP BY status, payment_status, DATE(created_at);

-- =============================================
-- 数据完整性约束
-- =============================================

-- 确保订单金额一致性
ALTER TABLE orders ADD CONSTRAINT check_final_amount 
CHECK (final_amount = total_amount + shipping_fee - discount_amount);

-- 确保库存不为负数
ALTER TABLE books ADD CONSTRAINT check_stock_positive 
CHECK (stock >= 0);

-- 确保价格为正数
ALTER TABLE books ADD CONSTRAINT check_price_positive 
CHECK (price > 0);

-- 确保订单商品数量为正数
ALTER TABLE order_items ADD CONSTRAINT check_quantity_positive 
CHECK (quantity > 0);

-- =============================================
-- 数据库性能优化
-- =============================================

-- 复合索引优化
CREATE INDEX idx_books_category_status ON books(category_id, status);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_messages_book_type ON messages(book_id, type) WHERE book_id IS NOT NULL;

-- 全文搜索索引
CREATE INDEX idx_books_search ON books USING GIN(
    to_tsvector('chinese', COALESCE(title, '') || ' ' || COALESCE(author, '') || ' ' || COALESCE(description, ''))
);

COMMENT ON DATABASE postgres IS '大学生收书卖书平台数据库';
COMMENT ON TABLE users IS '用户表 - 包含普通用户、管理员、超级管理员';
COMMENT ON TABLE categories IS '图书分类表 - 支持层级分类';
COMMENT ON TABLE books IS '图书表 - 核心业务数据';
COMMENT ON TABLE orders IS '订单表 - 包含完整订单信息和状态跟踪';
COMMENT ON TABLE order_items IS '订单详情表 - 订单中的具体商品信息';
COMMENT ON TABLE messages IS '消息表 - 用户咨询、聊天、系统通知';
COMMENT ON TABLE admin_logs IS '管理员操作日志表 - 记录所有管理员操作';
# 大学生收书卖书平台

一个专为大学生设计的二手书籍交易平台，提供便捷的收书卖书服务。

## 项目结构

```
大学生收书卖书/
├── backend/          # 后端API服务 (Node.js + Express)
├── frontend/         # 前端应用 (React + Vite)
├── database/         # 数据库相关文件 (PostgreSQL)
├── docs/            # 项目文档
├── docker/          # Docker配置文件
├── scripts/         # 构建和部署脚本
├── README.md        # 项目说明
└── package.json     # 根目录依赖管理
```

## 技术栈

### 后端
- Node.js + Express.js
- PostgreSQL 数据库
- JWT 认证
- Socket.io (实时通讯)

### 前端
- React 18
- Vite 构建工具
- TypeScript
- Tailwind CSS
- React Router

### 开发工具
- Docker & Docker Compose
- ESLint + Prettier
- Jest (测试)

## 快速开始

1. 克隆项目并安装依赖
```bash
git clone <repository-url>
cd 大学生收书卖书
npm install
```

2. 启动开发环境
```bash
npm run dev
```

## 环境要求

- Node.js >= 18
- PostgreSQL >= 14
- Docker (可选)

## 许可证

MIT License
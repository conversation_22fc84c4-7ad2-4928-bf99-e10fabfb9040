# 大学生收书卖书平台 - 技术架构图

## 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[学生用户]
        U2[管理员]
        U3[超级管理员]
    end
    
    subgraph "接入层"
        LB[负载均衡器]
        CDN[CDN]
        GW[API网关]
    end
    
    subgraph "应用层"
        subgraph "清华大学实例"
            T1[用户服务]
            T2[图书服务]
            T3[订单服务]
            T4[消息服务]
        end
        
        subgraph "北京大学实例"
            P1[用户服务]
            P2[图书服务]
            P3[订单服务]
            P4[消息服务]
        end
        
        subgraph "共享服务"
            S1[支付服务]
            S2[通知服务]
            S3[分析服务]
            S4[配置服务]
        end
    end
    
    subgraph "数据层"
        subgraph "学校数据库"
            DB1[(清华数据库)]
            DB2[(北大数据库)]
        end
        
        subgraph "共享数据库"
            DBS[(分析数据库)]
            DBC[(配置数据库)]
        end
        
        subgraph "缓存层"
            R1[Redis集群]
            R2[本地缓存]
        end
        
        subgraph "消息队列"
            MQ[RabbitMQ/Kafka]
        end
    end
    
    subgraph "监控层"
        M1[应用监控]
        M2[日志收集]
        M3[告警系统]
    end
    
    U1 --> LB
    U2 --> LB
    U3 --> LB
    LB --> CDN
    LB --> GW
    
    GW --> T1
    GW --> T2
    GW --> T3
    GW --> T4
    GW --> P1
    GW --> P2
    GW --> P3
    GW --> P4
    GW --> S1
    GW --> S2
    
    T1 --> DB1
    T2 --> DB1
    T3 --> DB1
    T4 --> DB1
    
    P1 --> DB2
    P2 --> DB2
    P3 --> DB2
    P4 --> DB2
    
    S1 --> DBS
    S2 --> DBS
    S3 --> DBS
    S4 --> DBC
    
    T1 --> R1
    T2 --> R1
    T3 --> R1
    P1 --> R1
    P2 --> R1
    P3 --> R1
    
    T4 --> MQ
    P4 --> MQ
    S2 --> MQ
    
    T1 --> M1
    T2 --> M1
    T3 --> M1
    P1 --> M1
    P2 --> M1
    P3 --> M1
```

## 微服务架构图

```mermaid
graph LR
    subgraph "API网关层"
        GW[API Gateway]
        AUTH[认证服务]
        RATE[限流服务]
    end
    
    subgraph "业务服务层"
        subgraph "用户域"
            US[用户服务]
            PS[个人资料服务]
            AS[认证授权服务]
        end
        
        subgraph "图书域"
            BS[图书服务]
            SS[搜索服务]
            IS[库存服务]
        end
        
        subgraph "交易域"
            OS[订单服务]
            PMS[支付服务]
            SHS[物流服务]
        end
        
        subgraph "沟通域"
            MS[消息服务]
            NS[通知服务]
            CS[聊天服务]
        end
    end
    
    subgraph "数据服务层"
        subgraph "数据库"
            UDB[(用户数据库)]
            BDB[(图书数据库)]
            ODB[(订单数据库)]
            MDB[(消息数据库)]
        end
        
        subgraph "缓存"
            RC[Redis缓存]
            MC[内存缓存]
        end
        
        subgraph "搜索"
            ES[Elasticsearch]
        end
    end
    
    subgraph "基础设施层"
        MQ[消息队列]
        LOG[日志服务]
        MON[监控服务]
        CFG[配置中心]
    end
    
    GW --> US
    GW --> BS
    GW --> OS
    GW --> MS
    
    US --> UDB
    US --> RC
    
    BS --> BDB
    BS --> RC
    BS --> ES
    
    OS --> ODB
    OS --> RC
    OS --> MQ
    
    MS --> MDB
    MS --> MQ
    
    PMS --> MQ
    NS --> MQ
    
    OS --> PMS
    OS --> SHS
    MS --> NS
```

## 数据库架构图

```mermaid
erDiagram
    USERS ||--o{ ORDERS : "places"
    USERS ||--o{ MESSAGES : "sends"
    USERS ||--o{ BOOKS : "manages"
    USERS ||--o{ ADMIN_LOGS : "performs"
    
    BOOKS ||--o{ ORDER_ITEMS : "included_in"
    BOOKS }o--|| CATEGORIES : "belongs_to"
    BOOKS ||--o{ MESSAGES : "about"
    
    ORDERS ||--o{ ORDER_ITEMS : "contains"
    ORDERS ||--o{ MESSAGES : "about"
    
    CATEGORIES ||--o{ CATEGORIES : "parent_of"
    
    MESSAGES }o--o| MESSAGES : "replies_to"
    
    USERS {
        uuid id PK
        string username
        string phone UK
        string email
        string password_hash
        enum role
        string avatar
        enum register_type
        string third_party_id
        boolean phone_verified
        boolean email_verified
        enum status
        uuid created_by FK
        string contact_wechat
        string contact_qq
        string contact_phone_public
        timestamp last_login_at
        timestamp created_at
        timestamp updated_at
    }
    
    BOOKS {
        uuid id PK
        string isbn
        string title
        string author
        string publisher
        date publication_date
        uuid category_id FK
        text description
        enum condition
        enum status
        decimal price
        decimal original_price
        integer stock
        integer sold_count
        text cover_image
        text[] images
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    ORDERS {
        uuid id PK
        string order_number UK
        uuid user_id FK
        decimal total_amount
        decimal shipping_fee
        decimal discount_amount
        decimal final_amount
        enum status
        enum payment_method
        enum payment_status
        timestamp paid_at
        text delivery_address
        string delivery_phone
        string delivery_name
        text delivery_notes
        string delivery_person
        string delivery_person_phone
        timestamp delivered_at
        text return_reason
        timestamp return_requested_at
        timestamp return_approved_at
        timestamp return_completed_at
        text notes
        text admin_notes
        uuid processed_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    ORDER_ITEMS {
        uuid id PK
        uuid order_id FK
        uuid book_id FK
        integer quantity
        decimal unit_price
        decimal subtotal
        string book_title
        string book_author
        string book_isbn
        text book_cover_image
        timestamp created_at
    }
    
    CATEGORIES {
        uuid id PK
        string name
        text description
        uuid parent_id FK
        integer sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    MESSAGES {
        uuid id PK
        uuid sender_id FK
        uuid receiver_id FK
        uuid book_id FK
        uuid order_id FK
        text content
        enum type
        enum status
        timestamp read_at
        boolean is_admin_reply
        uuid parent_message_id FK
        uuid reviewed_by FK
        timestamp reviewed_at
        timestamp created_at
        timestamp updated_at
    }
    
    ADMIN_LOGS {
        uuid id PK
        uuid admin_id FK
        enum action
        enum target_type
        uuid target_id
        text description
        inet ip_address
        text user_agent
        jsonb request_data
        boolean success
        text error_message
        timestamp created_at
    }
```

## 部署架构图

```mermaid
graph TB
    subgraph "外部用户"
        EU[外部用户]
    end
    
    subgraph "CDN + 负载均衡"
        CDN[CDN]
        LB[负载均衡器]
    end
    
    subgraph "Docker容器集群"
        subgraph "Web服务容器"
            WEB1[Web服务-1]
            WEB2[Web服务-2]
            WEB3[Web服务-3]
        end
        
        subgraph "API服务容器"
            API1[API服务-1]
            API2[API服务-2]
            API3[API服务-3]
        end
        
        subgraph "后台服务容器"
            BG1[后台任务服务]
            BG2[消息处理服务]
            BG3[定时任务服务]
        end
    end
    
    subgraph "数据库集群"
        subgraph "主数据库"
            PG_MASTER[(PostgreSQL Master)]
        end
        
        subgraph "从数据库"
            PG_SLAVE1[(PostgreSQL Slave 1)]
            PG_SLAVE2[(PostgreSQL Slave 2)]
        end
    end
    
    subgraph "缓存集群"
        REDIS_MASTER[Redis Master]
        REDIS_SLAVE[Redis Slave]
    end
    
    subgraph "消息队列"
        MQ_CLUSTER[RabbitMQ Cluster]
    end
    
    subgraph "监控系统"
        PROMETHEUS[Prometheus]
        GRAFANA[Grafana]
        ALERTMANAGER[AlertManager]
    end
    
    subgraph "日志系统"
        ELASTICSEARCH[Elasticsearch]
        LOGSTASH[Logstash]
        KIBANA[Kibana]
    end
    
    EU --> CDN
    CDN --> LB
    LB --> WEB1
    LB --> WEB2
    LB --> WEB3
    
    WEB1 --> API1
    WEB2 --> API2
    WEB3 --> API3
    
    API1 --> PG_MASTER
    API2 --> PG_SLAVE1
    API3 --> PG_SLAVE2
    
    PG_MASTER --> PG_SLAVE1
    PG_MASTER --> PG_SLAVE2
    
    API1 --> REDIS_MASTER
    API2 --> REDIS_MASTER
    API3 --> REDIS_MASTER
    
    REDIS_MASTER --> REDIS_SLAVE
    
    API1 --> MQ_CLUSTER
    BG1 --> MQ_CLUSTER
    BG2 --> MQ_CLUSTER
    
    WEB1 --> PROMETHEUS
    API1 --> PROMETHEUS
    PG_MASTER --> PROMETHEUS
    REDIS_MASTER --> PROMETHEUS
    
    PROMETHEUS --> GRAFANA
    PROMETHEUS --> ALERTMANAGER
    
    WEB1 --> LOGSTASH
    API1 --> LOGSTASH
    LOGSTASH --> ELASTICSEARCH
    ELASTICSEARCH --> KIBANA
```

## 安全架构图

```mermaid
graph TB
    subgraph "外部威胁"
        THREAT1[DDoS攻击]
        THREAT2[SQL注入]
        THREAT3[XSS攻击]
        THREAT4[数据泄露]
    end
    
    subgraph "安全防护层"
        subgraph "网络安全"
            WAF[Web应用防火墙]
            DDOS[DDoS防护]
            SSL[SSL/TLS加密]
        end
        
        subgraph "应用安全"
            RATE_LIMIT[速率限制]
            INPUT_VALID[输入验证]
            CSRF[CSRF防护]
            XSS_FILTER[XSS过滤]
        end
        
        subgraph "身份认证"
            JWT[JWT认证]
            OAUTH[OAuth2.0]
            MFA[多因素认证]
            RBAC[角色权限控制]
        end
        
        subgraph "数据安全"
            ENCRYPT[数据加密]
            BACKUP[安全备份]
            AUDIT[审计日志]
            PRIVACY[隐私保护]
        end
    end
    
    subgraph "应用服务"
        API[API服务]
        DB[(数据库)]
        CACHE[缓存服务]
        MQ[消息队列]
    end
    
    subgraph "监控告警"
        SECURITY_MON[安全监控]
        ALERT[入侵检测]
        LOG_ANALYSIS[日志分析]
    end
    
    THREAT1 --> DDOS
    THREAT2 --> WAF
    THREAT3 --> XSS_FILTER
    THREAT4 --> ENCRYPT
    
    DDOS --> SSL
    WAF --> RATE_LIMIT
    SSL --> INPUT_VALID
    
    RATE_LIMIT --> JWT
    INPUT_VALID --> OAUTH
    CSRF --> MFA
    XSS_FILTER --> RBAC
    
    JWT --> API
    OAUTH --> API
    MFA --> API
    RBAC --> API
    
    API --> DB
    API --> CACHE
    API --> MQ
    
    ENCRYPT --> DB
    BACKUP --> DB
    AUDIT --> DB
    
    API --> SECURITY_MON
    DB --> SECURITY_MON
    SECURITY_MON --> ALERT
    SECURITY_MON --> LOG_ANALYSIS
```
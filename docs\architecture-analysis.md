# 大学生收书卖书平台 - 技术架构深度分析

## 1. 多校区系统架构设计

### 1.1 架构原则
- **数据隔离**: 每个学校独立的数据库实例
- **服务共享**: 通用业务逻辑复用
- **独立部署**: 支持单校或多校部署模式
- **横向扩展**: 支持新增学校零配置接入

### 1.2 架构模式选择

#### 方案A: 单应用多租户架构
```
优势: 部署简单、资源利用率高、维护成本低
劣势: 数据隔离复杂、单点故障风险、扩展性受限
适用: 初期小规模部署（3-5所学校）
```

#### 方案B: 微服务多实例架构 (推荐)
```
优势: 完全隔离、独立扩展、故障隔离、技术栈灵活
劣势: 运维复杂、资源消耗大、一致性挑战
适用: 中大规模部署（5+所学校）
```

### 1.3 服务拆分设计

```typescript
// 核心服务模块
interface ServiceArchitecture {
  // 学校实例级别服务
  schoolServices: {
    userService: UserService;        // 用户管理
    bookService: BookService;        // 图书管理
    orderService: OrderService;      // 订单管理
    messageService: MessageService;  // 消息通信
  };
  
  // 共享服务
  sharedServices: {
    paymentService: PaymentService;     // 支付服务
    notificationService: NotificationService; // 消息推送
    analyticsService: AnalyticsService; // 数据分析
    configService: ConfigService;       // 配置管理
  };

  // 基础设施服务
  infrastructure: {
    gateway: APIGateway;           // API网关
    discovery: ServiceDiscovery;   // 服务发现
    monitoring: MonitoringService; // 监控服务
    logging: LoggingService;       // 日志服务
  };
}
```

### 1.4 部署架构

#### Docker Compose 多实例部署
```yaml
# docker-compose.multi-school.yml
version: '3.8'
services:
  # 清华大学实例
  tsinghua-app:
    image: bookstore-app:latest
    environment:
      - SCHOOL_ID=tsinghua
      - DB_HOST=tsinghua-db
      - REDIS_HOST=tsinghua-redis
    depends_on:
      - tsinghua-db
      - tsinghua-redis
    
  tsinghua-db:
    image: postgres:15
    environment:
      - POSTGRES_DB=bookstore_tsinghua
    volumes:
      - tsinghua_db:/var/lib/postgresql/data
  
  # 北京大学实例
  pku-app:
    image: bookstore-app:latest
    environment:
      - SCHOOL_ID=pku
      - DB_HOST=pku-db
      - REDIS_HOST=pku-redis
    depends_on:
      - pku-db
      - pku-redis
      
  # API网关
  gateway:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - tsinghua-app
      - pku-app

  # 共享服务
  notification-service:
    image: notification-service:latest
    environment:
      - REDIS_HOST=shared-redis
      
  analytics-service:
    image: analytics-service:latest
    environment:
      - ANALYTICS_DB=analytics_db
```

## 2. 数据隔离与共享策略

### 2.1 数据隔离设计

#### 物理隔离 (推荐)
```sql
-- 每个学校独立数据库
CREATE DATABASE bookstore_tsinghua;
CREATE DATABASE bookstore_pku;
CREATE DATABASE bookstore_ruc;

-- 共享数据库
CREATE DATABASE bookstore_shared;
```

#### 逻辑隔离
```sql
-- 在单数据库中通过 school_id 字段隔离
ALTER TABLE users ADD COLUMN school_id VARCHAR(50) NOT NULL;
ALTER TABLE books ADD COLUMN school_id VARCHAR(50) NOT NULL;
ALTER TABLE orders ADD COLUMN school_id VARCHAR(50) NOT NULL;

-- 行级安全策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY school_isolation ON users 
  FOR ALL TO application_role 
  USING (school_id = current_setting('app.school_id'));
```

### 2.2 数据共享模型

#### 共享数据类型
```typescript
interface SharedData {
  // 系统级配置
  systemConfig: {
    paymentMethods: PaymentMethod[];
    shippingMethods: ShippingMethod[];
    bookCategories: Category[];
  };
  
  // 跨校交易数据
  crossSchoolTrades: {
    bookExchange: BookExchange[];
    interSchoolOrders: Order[];
  };
  
  // 分析数据
  analyticsData: {
    platformMetrics: PlatformMetrics;
    schoolComparisons: SchoolComparison[];
    marketTrends: MarketTrend[];
  };
}
```

#### 数据同步策略
```typescript
// 事件驱动数据同步
class DataSyncService {
  async syncBookCategories(event: CategoryUpdateEvent) {
    // 同步到所有学校实例
    const schools = await this.getActiveSchools();
    await Promise.all(
      schools.map(school => 
        this.updateSchoolCategories(school.id, event.categories)
      )
    );
  }
  
  async syncPaymentMethods(event: PaymentMethodUpdateEvent) {
    // 通过消息队列广播更新
    await this.messageQueue.publish('payment.methods.updated', event);
  }
}
```

## 3. 扩展性设计

### 3.1 水平扩展架构

#### 微服务扩展模式
```typescript
interface ScalabilityDesign {
  // 服务级扩展
  serviceScaling: {
    userService: {
      instances: number;
      loadBalancer: 'round-robin' | 'least-connections';
      autoScaling: AutoScalingConfig;
    };
    bookService: {
      instances: number;
      caching: CacheStrategy;
      database: DatabaseSharding;
    };
  };
  
  // 数据库扩展
  databaseScaling: {
    readReplicas: number;
    sharding: ShardingStrategy;
    caching: CacheLayer;
  };
  
  // 存储扩展
  storageScaling: {
    fileStorage: 'local' | 'oss' | 'cdn';
    imageOptimization: ImageOptimizationConfig;
  };
}
```

#### 自动扩展配置
```yaml
# kubernetes 自动扩展
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bookstore-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bookstore-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 3.2 学校接入流程

#### 零配置接入
```typescript
class SchoolOnboardingService {
  async onboardNewSchool(schoolInfo: SchoolInfo): Promise<SchoolInstance> {
    // 1. 创建学校专用数据库
    await this.createSchoolDatabase(schoolInfo.id);
    
    // 2. 初始化数据结构
    await this.initializeSchoolSchema(schoolInfo.id);
    
    // 3. 创建默认管理员账号
    await this.createDefaultAdmin(schoolInfo);
    
    // 4. 配置服务实例
    const instance = await this.deploySchoolInstance(schoolInfo);
    
    // 5. 注册到服务发现
    await this.registerService(instance);
    
    // 6. 配置负载均衡
    await this.updateLoadBalancer(instance);
    
    return instance;
  }
}
```

## 4. 性能优化方案

### 4.1 数据库性能优化

#### 索引优化策略
```sql
-- 复合索引优化查询
CREATE INDEX idx_books_school_category_status 
ON books(school_id, category_id, status, created_at DESC);

CREATE INDEX idx_orders_user_status_date 
ON orders(user_id, status, created_at DESC);

-- 部分索引减少存储
CREATE INDEX idx_active_books 
ON books(title, author) WHERE status = 'available';

-- 表达式索引支持复杂查询
CREATE INDEX idx_books_search 
ON books USING GIN(to_tsvector('chinese', title || ' ' || author));
```

#### 分库分表策略
```typescript
interface DatabaseSharding {
  // 垂直分片 - 按业务模块
  verticalSharding: {
    userDB: 'user_service_db';
    bookDB: 'book_service_db';
    orderDB: 'order_service_db';
    messageDB: 'message_service_db';
  };
  
  // 水平分片 - 按学校
  horizontalSharding: {
    shardKey: 'school_id';
    shardCount: number;
    shardMapping: Record<string, string>;
  };
}

class DatabaseShardingService {
  getShardBySchool(schoolId: string): DatabaseConnection {
    return this.schoolDatabases[schoolId] || this.defaultDatabase;
  }
  
  async queryAcrossShards(query: string): Promise<any[]> {
    const results = await Promise.all(
      Object.values(this.schoolDatabases).map(db => 
        db.query(query)
      )
    );
    return results.flat();
  }
}
```

### 4.2 缓存策略

#### 多级缓存架构
```typescript
interface CacheStrategy {
  // L1: 应用级缓存 (内存)
  applicationCache: {
    categories: CacheConfig;
    userSessions: CacheConfig;
    bookSearchResults: CacheConfig;
  };
  
  // L2: 分布式缓存 (Redis)
  distributedCache: {
    bookDetails: CacheConfig;
    userProfiles: CacheConfig;
    orderStatistics: CacheConfig;
  };
  
  // L3: CDN缓存 (静态资源)
  cdnCache: {
    bookImages: CDNConfig;
    staticAssets: CDNConfig;
  };
}

class CacheService {
  async getBookDetails(bookId: string): Promise<Book> {
    // L1 缓存检查
    let book = this.memoryCache.get(`book:${bookId}`);
    if (book) return book;
    
    // L2 缓存检查
    book = await this.redisCache.get(`book:${bookId}`);
    if (book) {
      this.memoryCache.set(`book:${bookId}`, book, 300); // 5分钟
      return book;
    }
    
    // 数据库查询
    book = await this.database.getBook(bookId);
    
    // 写入缓存
    await this.redisCache.set(`book:${bookId}`, book, 3600); // 1小时
    this.memoryCache.set(`book:${bookId}`, book, 300);
    
    return book;
  }
}
```

### 4.3 并发处理

#### 连接池优化
```typescript
interface ConnectionPoolConfig {
  database: {
    min: 5;
    max: 50;
    acquireTimeoutMillis: 60000;
    idleTimeoutMillis: 30000;
  };
  redis: {
    min: 2;
    max: 20;
    retryDelayOnFailover: 1000;
  };
}

class DatabaseManager {
  private pools: Map<string, Pool> = new Map();
  
  async getConnection(schoolId: string): Promise<PoolClient> {
    let pool = this.pools.get(schoolId);
    if (!pool) {
      pool = new Pool({
        ...this.config,
        database: `bookstore_${schoolId}`,
      });
      this.pools.set(schoolId, pool);
    }
    
    return pool.connect();
  }
}
```

## 5. 安全架构设计

### 5.1 身份认证与授权

#### JWT + OAuth2 认证架构
```typescript
interface AuthenticationService {
  // JWT 配置
  jwtConfig: {
    accessTokenExpiry: '15m';
    refreshTokenExpiry: '7d';
    algorithm: 'RS256';
    issuer: 'bookstore-platform';
  };
  
  // OAuth2 提供商
  oauthProviders: {
    wechat: WeChatOAuthConfig;
    qq: QQOAuthConfig;
    alipay: AlipayOAuthConfig;
  };
  
  // 多因素认证
  mfaConfig: {
    smsEnabled: boolean;
    emailEnabled: boolean;
    totpEnabled: boolean;
  };
}

class AuthService {
  async authenticateUser(credentials: LoginCredentials): Promise<AuthResult> {
    // 1. 验证凭据
    const user = await this.validateCredentials(credentials);
    if (!user) throw new UnauthorizedError('Invalid credentials');
    
    // 2. 检查账号状态
    if (user.status !== 'active') {
      throw new ForbiddenError('Account is inactive');
    }
    
    // 3. 生成令牌
    const tokens = await this.generateTokens(user);
    
    // 4. 记录登录日志
    await this.logUserLogin(user, credentials.ipAddress);
    
    return { user, tokens };
  }
  
  async generateTokens(user: User): Promise<TokenPair> {
    const payload = {
      userId: user.id,
      schoolId: user.schoolId,
      role: user.role,
      permissions: await this.getUserPermissions(user),
    };
    
    const accessToken = jwt.sign(payload, this.privateKey, {
      expiresIn: this.config.accessTokenExpiry,
      issuer: this.config.issuer,
      audience: user.schoolId,
    });
    
    const refreshToken = jwt.sign(
      { userId: user.id }, 
      this.privateKey, 
      { expiresIn: this.config.refreshTokenExpiry }
    );
    
    return { accessToken, refreshToken };
  }
}
```

### 5.2 数据安全

#### 数据加密策略
```typescript
interface DataEncryption {
  // 静态数据加密
  atRest: {
    database: 'AES-256-GCM';
    fileStorage: 'AES-256-GCM';
    backups: 'AES-256-GCM';
  };
  
  // 传输加密
  inTransit: {
    api: 'TLS 1.3';
    database: 'SSL/TLS';
    messageQueue: 'SSL/TLS';
  };
  
  // 应用级加密
  application: {
    personalData: 'AES-256-GCM';
    paymentInfo: 'RSA-4096';
    sessionData: 'ChaCha20-Poly1305';
  };
}

class EncryptionService {
  async encryptPersonalData(data: PersonalData): Promise<EncryptedData> {
    const key = await this.getEncryptionKey(data.userId);
    const encrypted = await crypto.encrypt(JSON.stringify(data), key);
    return {
      data: encrypted,
      algorithm: 'AES-256-GCM',
      keyId: key.id,
    };
  }
  
  async decryptPersonalData(encrypted: EncryptedData): Promise<PersonalData> {
    const key = await this.getEncryptionKey(encrypted.keyId);
    const decrypted = await crypto.decrypt(encrypted.data, key);
    return JSON.parse(decrypted);
  }
}
```

### 5.3 API安全

#### 请求安全中间件
```typescript
class APISecurityMiddleware {
  // 速率限制
  rateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 最多100次请求
    message: 'Too many requests',
    standardHeaders: true,
    legacyHeaders: false,
  });
  
  // 输入验证
  async validateInput(req: Request, res: Response, next: NextFunction) {
    const schema = this.getValidationSchema(req.route.path);
    try {
      req.body = await schema.validate(req.body);
      next();
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
  
  // SQL注入防护
  async preventSQLInjection(req: Request, res: Response, next: NextFunction) {
    const suspiciousPatterns = [
      /(\b(union|select|insert|update|delete|drop|create|alter)\b)/i,
      /(--|\*|\/\*|\*\/|;)/,
      /(\b(and|or)\b.*=.*)/i,
    ];
    
    const queryString = JSON.stringify(req.query) + JSON.stringify(req.body);
    const hasSQLInjection = suspiciousPatterns.some(pattern => 
      pattern.test(queryString)
    );
    
    if (hasSQLInjection) {
      await this.logSecurityIncident('SQL_INJECTION_ATTEMPT', req);
      return res.status(403).json({ error: 'Forbidden request' });
    }
    
    next();
  }
}
```

## 6. 微服务设计

### 6.1 服务边界划分

#### 领域驱动设计
```typescript
interface DomainServices {
  // 用户域
  userDomain: {
    userService: 'user-management';
    authService: 'authentication';
    profileService: 'user-profile';
  };
  
  // 图书域
  bookDomain: {
    catalogService: 'book-catalog';
    inventoryService: 'inventory-management';
    searchService: 'book-search';
  };
  
  // 交易域
  tradeDomain: {
    orderService: 'order-management';
    paymentService: 'payment-processing';
    shippingService: 'shipping-management';
  };
  
  // 通信域
  communicationDomain: {
    messageService: 'messaging';
    notificationService: 'notifications';
    chatService: 'real-time-chat';
  };
}
```

#### 服务依赖关系
```mermaid
graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Book Service]
    A --> D[Order Service]
    A --> E[Message Service]
    
    D --> B
    D --> C
    D --> F[Payment Service]
    D --> G[Shipping Service]
    
    E --> B
    E --> C
    E --> H[Notification Service]
    
    I[Analytics Service] --> B
    I --> C
    I --> D
    I --> E
```

### 6.2 服务通信

#### 同步通信 - HTTP/gRPC
```typescript
interface ServiceCommunication {
  // HTTP REST API
  httpAPI: {
    userService: 'http://user-service:3001';
    bookService: 'http://book-service:3002';
    orderService: 'http://order-service:3003';
  };
  
  // gRPC (高性能内部通信)
  grpcServices: {
    paymentService: 'payment-service:50051';
    inventoryService: 'inventory-service:50052';
    analyticsService: 'analytics-service:50053';
  };
}

// gRPC 服务定义
// payment.proto
service PaymentService {
  rpc ProcessPayment(PaymentRequest) returns (PaymentResponse);
  rpc RefundPayment(RefundRequest) returns (RefundResponse);
  rpc GetPaymentStatus(PaymentStatusRequest) returns (PaymentStatusResponse);
}

class PaymentClient {
  private client: PaymentServiceClient;
  
  async processPayment(order: Order): Promise<PaymentResult> {
    const request: PaymentRequest = {
      orderId: order.id,
      amount: order.totalAmount,
      paymentMethod: order.paymentMethod,
    };
    
    const response = await this.client.processPayment(request);
    return response;
  }
}
```

#### 异步通信 - 消息队列
```typescript
interface MessageQueues {
  // 事件发布订阅
  eventBus: {
    orderEvents: 'order-events';
    paymentEvents: 'payment-events';
    inventoryEvents: 'inventory-events';
    userEvents: 'user-events';
  };
  
  // 任务队列
  taskQueues: {
    emailQueue: 'email-notifications';
    smsQueue: 'sms-notifications';
    reportQueue: 'report-generation';
    backupQueue: 'data-backup';
  };
}

class EventPublisher {
  async publishOrderCreated(order: Order) {
    const event: OrderCreatedEvent = {
      eventId: uuid(),
      eventType: 'ORDER_CREATED',
      aggregateId: order.id,
      data: order,
      timestamp: new Date(),
    };
    
    await this.messageQueue.publish('order-events', event);
  }
}

class EventSubscriber {
  @Subscribe('order-events')
  async handleOrderCreated(event: OrderCreatedEvent) {
    // 减少库存
    await this.inventoryService.reserveBooks(event.data.items);
    
    // 发送确认邮件
    await this.emailService.sendOrderConfirmation(event.data);
    
    // 更新分析数据
    await this.analyticsService.recordOrderMetrics(event.data);
  }
}
```

## 7. 数据库设计优化

### 7.1 多租户数据库架构

#### 数据库隔离策略对比
```typescript
interface DatabaseIsolationStrategies {
  // 策略1: 独立数据库 (推荐)
  separateDatabase: {
    isolation: 'Complete';
    scalability: 'High';
    maintenance: 'Complex';
    cost: 'High';
    security: 'Excellent';
  };
  
  // 策略2: 共享数据库独立Schema
  separateSchema: {
    isolation: 'Good';
    scalability: 'Medium';
    maintenance: 'Medium';
    cost: 'Medium';
    security: 'Good';
  };
  
  // 策略3: 共享表 + Tenant ID
  sharedTable: {
    isolation: 'Basic';
    scalability: 'Low';
    maintenance: 'Simple';
    cost: 'Low';
    security: 'Fair';
  };
}
```

#### 数据库连接管理
```typescript
class MultiTenantDatabaseManager {
  private connectionPools = new Map<string, Pool>();
  
  async getConnection(schoolId: string): Promise<PoolClient> {
    if (!this.connectionPools.has(schoolId)) {
      await this.createSchoolPool(schoolId);
    }
    
    const pool = this.connectionPools.get(schoolId)!;
    return pool.connect();
  }
  
  private async createSchoolPool(schoolId: string): Promise<void> {
    const config = {
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      database: `bookstore_${schoolId}`,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      min: 2,
      max: 20,
    };
    
    const pool = new Pool(config);
    this.connectionPools.set(schoolId, pool);
  }
  
  async queryWithTenant<T>(
    schoolId: string, 
    query: string, 
    params?: any[]
  ): Promise<T[]> {
    const client = await this.getConnection(schoolId);
    try {
      const result = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }
}
```

### 7.2 读写分离架构

```typescript
interface DatabaseCluster {
  master: DatabaseConfig;
  readReplicas: DatabaseConfig[];
  loadBalancer: LoadBalancerConfig;
}

class DatabaseClusterManager {
  private masterPool: Pool;
  private replicaPools: Pool[] = [];
  private currentReplicaIndex = 0;
  
  constructor(private config: DatabaseCluster) {
    this.masterPool = new Pool(config.master);
    this.replicaPools = config.readReplicas.map(config => new Pool(config));
  }
  
  async executeWrite(query: string, params?: any[]): Promise<any> {
    const client = await this.masterPool.connect();
    try {
      return await client.query(query, params);
    } finally {
      client.release();
    }
  }
  
  async executeRead(query: string, params?: any[]): Promise<any> {
    // 轮询读取副本
    const pool = this.replicaPools[this.currentReplicaIndex];
    this.currentReplicaIndex = (this.currentReplicaIndex + 1) % this.replicaPools.length;
    
    const client = await pool.connect();
    try {
      return await client.query(query, params);
    } catch (error) {
      // 如果副本失败，回退到主库
      console.warn('Read replica failed, falling back to master:', error);
      return this.executeWrite(query, params);
    } finally {
      client.release();
    }
  }
}
```

## 8. 监控与运维

### 8.1 系统监控

#### 监控指标体系
```typescript
interface MonitoringMetrics {
  // 系统指标
  systemMetrics: {
    cpu: CPUMetrics;
    memory: MemoryMetrics;
    disk: DiskMetrics;
    network: NetworkMetrics;
  };
  
  // 应用指标
  applicationMetrics: {
    requestRate: number;
    responseTime: number;
    errorRate: number;
    activeUsers: number;
  };
  
  // 业务指标
  businessMetrics: {
    orderVolume: number;
    revenuePerHour: number;
    userRegistrations: number;
    bookUploads: number;
  };
  
  // 数据库指标
  databaseMetrics: {
    connectionCount: number;
    queryLatency: number;
    slowQueries: number;
    lockWaits: number;
  };
}

class MetricsCollector {
  async collectSystemMetrics(): Promise<SystemMetrics> {
    return {
      cpu: await this.getCPUUsage(),
      memory: await this.getMemoryUsage(),
      disk: await this.getDiskUsage(),
      network: await this.getNetworkStats(),
    };
  }
  
  async collectApplicationMetrics(): Promise<ApplicationMetrics> {
    const stats = await this.getRequestStats();
    return {
      requestRate: stats.requestsPerSecond,
      responseTime: stats.averageResponseTime,
      errorRate: stats.errorRate,
      activeUsers: await this.getActiveUserCount(),
    };
  }
}
```

### 8.2 日志管理

#### 结构化日志
```typescript
interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  service: string;
  schoolId?: string;
  userId?: string;
  traceId: string;
  message: string;
  data?: any;
  error?: Error;
}

class StructuredLogger {
  log(level: LogLevel, message: string, data?: any, error?: Error) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service: this.serviceName,
      schoolId: this.getCurrentSchoolId(),
      userId: this.getCurrentUserId(),
      traceId: this.getTraceId(),
      message,
      data,
      error,
    };
    
    // 输出到不同目标
    console.log(JSON.stringify(entry));
    
    // 发送到日志聚合服务
    this.sendToLogAggregator(entry);
    
    // 严重错误发送告警
    if (level === 'error') {
      this.sendAlert(entry);
    }
  }
}
```

### 8.3 健康检查

```typescript
interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: ServiceHealth[];
  dependencies: DependencyHealth[];
}

class HealthCheckService {
  async performHealthCheck(): Promise<HealthCheck> {
    const services = await this.checkServices();
    const dependencies = await this.checkDependencies();
    
    const overallStatus = this.calculateOverallStatus(services, dependencies);
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      services,
      dependencies,
    };
  }
  
  private async checkServices(): Promise<ServiceHealth[]> {
    return Promise.all([
      this.checkUserService(),
      this.checkBookService(),
      this.checkOrderService(),
      this.checkPaymentService(),
    ]);
  }
  
  private async checkDependencies(): Promise<DependencyHealth[]> {
    return Promise.all([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkMessageQueue(),
      this.checkExternalAPIs(),
    ]);
  }
}
```
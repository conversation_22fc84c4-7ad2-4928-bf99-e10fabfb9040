# 技术实现方案详细规范

## API网关实现

### Nginx配置示例
```nginx
# /etc/nginx/sites-available/bookstore-gateway
upstream tsinghua_backend {
    server tsinghua-app:3000 weight=3;
    server tsinghua-app-2:3000 weight=2;
    keepalive 32;
}

upstream pku_backend {
    server pku-app:3000 weight=3;
    server pku-app-2:3000 weight=2;
    keepalive 32;
}

# 根据域名路由到不同学校
server {
    listen 80;
    server_name tsinghua.bookstore.com;
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=tsinghua_limit:10m rate=10r/s;
    limit_req zone=tsinghua_limit burst=20 nodelay;
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # API请求
    location /api/ {
        proxy_pass http://tsinghua_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-School-ID "tsinghua";
        
        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500;
        proxy_connect_timeout 2s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # WebSocket支持
    location /ws/ {
        proxy_pass http://tsinghua_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

server {
    listen 80;
    server_name pku.bookstore.com;
    
    limit_req_zone $binary_remote_addr zone=pku_limit:10m rate=10r/s;
    limit_req zone=pku_limit burst=20 nodelay;
    
    location /api/ {
        proxy_pass http://pku_backend;
        proxy_set_header X-School-ID "pku";
        # 其他配置同上...
    }
}

# 管理后台 - 统一入口
server {
    listen 80;
    server_name admin.bookstore.com;
    
    # 更严格的限流
    limit_req_zone $binary_remote_addr zone=admin_limit:10m rate=5r/s;
    limit_req zone=admin_limit burst=10 nodelay;
    
    # IP白名单（可选）
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
    
    location / {
        # 根据请求头路由到对应学校
        set $backend "";
        if ($http_x_school_id = "tsinghua") {
            set $backend "tsinghua_backend";
        }
        if ($http_x_school_id = "pku") {
            set $backend "pku_backend";
        }
        
        proxy_pass http://$backend;
        proxy_set_header X-School-ID $http_x_school_id;
    }
}
```

### Express.js API网关实现
```typescript
// src/gateway/app.ts
import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';

const app = express();

// 安全中间件
app.use(helmet());
app.use(cors({
  origin: ['https://tsinghua.bookstore.com', 'https://pku.bookstore.com'],
  credentials: true,
}));

// 全局限流
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: { error: 'Too many requests' },
});
app.use(globalLimiter);

// 学校路由解析中间件
app.use((req, res, next) => {
  const host = req.get('host');
  const schoolId = host?.split('.')[0]; // 从子域名提取学校ID
  
  if (schoolId && ['tsinghua', 'pku', 'ruc'].includes(schoolId)) {
    req.schoolId = schoolId;
    req.headers['x-school-id'] = schoolId;
  } else {
    return res.status(400).json({ error: 'Invalid school domain' });
  }
  
  next();
});

// 服务发现配置
const serviceRegistry = {
  tsinghua: {
    host: process.env.TSINGHUA_SERVICE_HOST || 'tsinghua-app',
    port: process.env.TSINGHUA_SERVICE_PORT || 3000,
  },
  pku: {
    host: process.env.PKU_SERVICE_HOST || 'pku-app',
    port: process.env.PKU_SERVICE_PORT || 3000,
  },
};

// 动态代理配置
app.use('/api', (req, res, next) => {
  const schoolId = req.schoolId as string;
  const service = serviceRegistry[schoolId];
  
  if (!service) {
    return res.status(404).json({ error: 'Service not found' });
  }
  
  const proxy = createProxyMiddleware({
    target: `http://${service.host}:${service.port}`,
    changeOrigin: true,
    pathRewrite: {
      '^/api': '/api/v1', // 重写路径
    },
    onProxyReq: (proxyReq, req, res) => {
      // 添加追踪信息
      proxyReq.setHeader('X-Request-ID', req.id);
      proxyReq.setHeader('X-Forwarded-For', req.ip);
    },
    onError: (err, req, res) => {
      console.error(`Proxy error for ${schoolId}:`, err);
      res.status(503).json({ error: 'Service temporarily unavailable' });
    },
  });
  
  proxy(req, res, next);
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

export default app;
```

## 微服务基础框架

### 服务基类实现
```typescript
// src/common/BaseService.ts
import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { DatabaseManager } from './DatabaseManager';
import { CacheManager } from './CacheManager';
import { MessageQueue } from './MessageQueue';

export abstract class BaseService extends EventEmitter {
  protected logger: Logger;
  protected db: DatabaseManager;
  protected cache: CacheManager;
  protected messageQueue: MessageQueue;
  
  constructor(
    protected serviceName: string,
    protected schoolId: string
  ) {
    super();
    this.logger = this.createLogger();
    this.db = new DatabaseManager(schoolId);
    this.cache = new CacheManager(schoolId);
    this.messageQueue = new MessageQueue(schoolId);
  }
  
  abstract async initialize(): Promise<void>;
  abstract async shutdown(): Promise<void>;
  
  protected createLogger(): Logger {
    return createLogger({
      service: this.serviceName,
      schoolId: this.schoolId,
      level: process.env.LOG_LEVEL || 'info',
    });
  }
  
  protected async publishEvent(eventName: string, data: any): Promise<void> {
    const event = {
      eventId: generateUUID(),
      eventName,
      serviceName: this.serviceName,
      schoolId: this.schoolId,
      data,
      timestamp: new Date().toISOString(),
    };
    
    await this.messageQueue.publish(eventName, event);
    this.emit(eventName, event);
  }
  
  protected async withTransaction<T>(
    callback: (tx: Transaction) => Promise<T>
  ): Promise<T> {
    const tx = await this.db.beginTransaction();
    try {
      const result = await callback(tx);
      await tx.commit();
      return result;
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }
  
  protected async withCache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    const cached = await this.cache.get<T>(key);
    if (cached) return cached;
    
    const data = await fetcher();
    await this.cache.set(key, data, ttl);
    return data;
  }
}
```

### 用户服务实现示例
```typescript
// src/services/UserService.ts
import { BaseService } from '../common/BaseService';
import { User, CreateUserRequest, UpdateUserRequest } from '../types/User';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

export class UserService extends BaseService {
  constructor(schoolId: string) {
    super('UserService', schoolId);
  }
  
  async initialize(): Promise<void> {
    this.logger.info('Initializing UserService');
    await this.db.connect();
    await this.cache.connect();
    await this.messageQueue.connect();
  }
  
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down UserService');
    await this.db.disconnect();
    await this.cache.disconnect();
    await this.messageQueue.disconnect();
  }
  
  async createUser(request: CreateUserRequest): Promise<User> {
    this.logger.info('Creating user', { phone: request.phone });
    
    return this.withTransaction(async (tx) => {
      // 检查用户是否已存在
      const existingUser = await tx.findOne(
        'SELECT id FROM users WHERE phone = $1',
        [request.phone]
      );
      
      if (existingUser) {
        throw new Error('User already exists');
      }
      
      // 密码加密
      const passwordHash = await bcrypt.hash(request.password, 10);
      
      // 创建用户
      const user = await tx.query<User>(
        `INSERT INTO users (phone, email, password_hash, role, status)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [
          request.phone,
          request.email,
          passwordHash,
          request.role || 'user',
          'active'
        ]
      );
      
      // 发布用户创建事件
      await this.publishEvent('user.created', {
        userId: user[0].id,
        phone: user[0].phone,
        role: user[0].role,
      });
      
      // 清除相关缓存
      await this.cache.delete(`user:phone:${request.phone}`);
      
      return user[0];
    });
  }
  
  async authenticateUser(phone: string, password: string): Promise<{
    user: User;
    accessToken: string;
    refreshToken: string;
  }> {
    this.logger.info('Authenticating user', { phone });
    
    // 使用缓存提高性能
    const user = await this.withCache(
      `user:phone:${phone}`,
      async () => {
        const result = await this.db.query<User>(
          'SELECT * FROM users WHERE phone = $1 AND status = $2',
          [phone, 'active']
        );
        return result[0];
      },
      300 // 5分钟缓存
    );
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      throw new Error('Invalid password');
    }
    
    // 生成JWT令牌
    const tokens = await this.generateTokens(user);
    
    // 更新最后登录时间
    await this.db.query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );
    
    // 发布登录事件
    await this.publishEvent('user.logged_in', {
      userId: user.id,
      loginTime: new Date().toISOString(),
    });
    
    return { user, ...tokens };
  }
  
  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const payload = {
      userId: user.id,
      schoolId: this.schoolId,
      role: user.role,
    };
    
    const accessToken = jwt.sign(payload, process.env.JWT_SECRET!, {
      expiresIn: '15m',
      issuer: 'bookstore-platform',
      audience: this.schoolId,
    });
    
    const refreshToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );
    
    // 将refresh token存储到Redis
    await this.cache.set(
      `refresh_token:${user.id}`,
      refreshToken,
      7 * 24 * 3600 // 7天
    );
    
    return { accessToken, refreshToken };
  }
  
  async getUserById(userId: string): Promise<User | null> {
    return this.withCache(
      `user:${userId}`,
      async () => {
        const result = await this.db.query<User>(
          'SELECT * FROM users WHERE id = $1',
          [userId]
        );
        return result[0] || null;
      },
      3600 // 1小时缓存
    );
  }
  
  async updateUser(
    userId: string,
    updates: UpdateUserRequest
  ): Promise<User> {
    return this.withTransaction(async (tx) => {
      const user = await tx.query<User>(
        `UPDATE users 
         SET email = COALESCE($2, email),
             avatar = COALESCE($3, avatar),
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING *`,
        [userId, updates.email, updates.avatar]
      );
      
      if (!user[0]) {
        throw new Error('User not found');
      }
      
      // 清除缓存
      await this.cache.delete(`user:${userId}`);
      
      // 发布更新事件
      await this.publishEvent('user.updated', {
        userId,
        updates,
      });
      
      return user[0];
    });
  }
}
```

## 数据库管理器实现

### 多租户数据库管理
```typescript
// src/common/DatabaseManager.ts
import { Pool, PoolClient, QueryResult } from 'pg';
import { Logger } from 'winston';

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
  max?: number;
  min?: number;
}

export class DatabaseManager {
  private pool: Pool;
  private logger: Logger;
  
  constructor(private schoolId: string) {
    this.logger = createLogger({
      service: 'DatabaseManager',
      schoolId,
    });
    
    this.pool = new Pool(this.getConfig());
    this.setupEventHandlers();
  }
  
  private getConfig(): DatabaseConfig {
    return {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.NODE_ENV === 'test' 
        ? `bookstore_${this.schoolId}_test`
        : `bookstore_${this.schoolId}`,
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      ssl: process.env.NODE_ENV === 'production',
      max: 20, // 最大连接数
      min: 5,  // 最小连接数
    };
  }
  
  private setupEventHandlers(): void {
    this.pool.on('connect', (client) => {
      this.logger.info('New database connection established');
    });
    
    this.pool.on('error', (err) => {
      this.logger.error('Database pool error:', err);
    });
    
    this.pool.on('remove', () => {
      this.logger.info('Database connection removed from pool');
    });
  }
  
  async connect(): Promise<void> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT NOW()'); // 测试连接
      client.release();
      this.logger.info('Database connection successful');
    } catch (error) {
      this.logger.error('Failed to connect to database:', error);
      throw error;
    }
  }
  
  async disconnect(): Promise<void> {
    await this.pool.end();
    this.logger.info('Database connections closed');
  }
  
  async query<T = any>(
    text: string,
    params?: any[]
  ): Promise<T[]> {
    const start = Date.now();
    const client = await this.pool.connect();
    
    try {
      const result: QueryResult<T> = await client.query(text, params);
      const duration = Date.now() - start;
      
      this.logger.debug('Query executed', {
        query: text,
        params,
        duration,
        rowCount: result.rowCount,
      });
      
      return result.rows;
    } catch (error) {
      this.logger.error('Query failed', {
        query: text,
        params,
        error: error.message,
      });
      throw error;
    } finally {
      client.release();
    }
  }
  
  async findOne<T = any>(
    text: string,
    params?: any[]
  ): Promise<T | null> {
    const results = await this.query<T>(text, params);
    return results[0] || null;
  }
  
  async beginTransaction(): Promise<Transaction> {
    const client = await this.pool.connect();
    await client.query('BEGIN');
    
    return new Transaction(client, this.logger);
  }
  
  // 健康检查
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      const start = Date.now();
      await this.query('SELECT 1');
      const responseTime = Date.now() - start;
      
      const poolInfo = {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount,
      };
      
      return {
        status: 'healthy',
        details: {
          responseTime,
          pool: poolInfo,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }
}

export class Transaction {
  constructor(
    private client: PoolClient,
    private logger: Logger
  ) {}
  
  async query<T = any>(
    text: string,
    params?: any[]
  ): Promise<T[]> {
    try {
      const result: QueryResult<T> = await this.client.query(text, params);
      return result.rows;
    } catch (error) {
      this.logger.error('Transaction query failed', {
        query: text,
        params,
        error: error.message,
      });
      throw error;
    }
  }
  
  async findOne<T = any>(
    text: string,
    params?: any[]
  ): Promise<T | null> {
    const results = await this.query<T>(text, params);
    return results[0] || null;
  }
  
  async commit(): Promise<void> {
    try {
      await this.client.query('COMMIT');
      this.logger.debug('Transaction committed');
    } finally {
      this.client.release();
    }
  }
  
  async rollback(): Promise<void> {
    try {
      await this.client.query('ROLLBACK');
      this.logger.debug('Transaction rolled back');
    } finally {
      this.client.release();
    }
  }
}
```

## 缓存管理器实现

```typescript
// src/common/CacheManager.ts
import Redis from 'ioredis';
import { Logger } from 'winston';

export class CacheManager {
  private redis: Redis;
  private logger: Logger;
  
  constructor(private schoolId: string) {
    this.logger = createLogger({
      service: 'CacheManager',
      schoolId,
    });
    
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: this.getRedisDB(),
      retryDelayOnFailover: 1000,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });
    
    this.setupEventHandlers();
  }
  
  private getRedisDB(): number {
    // 为每个学校分配不同的Redis数据库
    const dbMap: Record<string, number> = {
      tsinghua: 0,
      pku: 1,
      ruc: 2,
      default: 15,
    };
    
    return dbMap[this.schoolId] || dbMap.default;
  }
  
  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      this.logger.info('Redis connection established');
    });
    
    this.redis.on('error', (err) => {
      this.logger.error('Redis connection error:', err);
    });
    
    this.redis.on('close', () => {
      this.logger.warn('Redis connection closed');
    });
  }
  
  async connect(): Promise<void> {
    await this.redis.connect();
    this.logger.info('Redis connected successfully');
  }
  
  async disconnect(): Promise<void> {
    await this.redis.disconnect();
    this.logger.info('Redis disconnected');
  }
  
  private getKey(key: string): string {
    return `${this.schoolId}:${key}`;
  }
  
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(this.getKey(key));
      if (!value) return null;
      
      return JSON.parse(value);
    } catch (error) {
      this.logger.error('Cache get error:', { key, error: error.message });
      return null;
    }
  }
  
  async set(
    key: string,
    value: any,
    ttl: number = 3600
  ): Promise<void> {
    try {
      await this.redis.setex(
        this.getKey(key),
        ttl,
        JSON.stringify(value)
      );
    } catch (error) {
      this.logger.error('Cache set error:', { key, error: error.message });
    }
  }
  
  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(this.getKey(key));
    } catch (error) {
      this.logger.error('Cache delete error:', { key, error: error.message });
    }
  }
  
  async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(this.getKey(pattern));
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      this.logger.error('Cache delete pattern error:', { pattern, error: error.message });
    }
  }
  
  async increment(key: string, by: number = 1): Promise<number> {
    try {
      return await this.redis.incrby(this.getKey(key), by);
    } catch (error) {
      this.logger.error('Cache increment error:', { key, error: error.message });
      throw error;
    }
  }
  
  async setIfNotExists(
    key: string,
    value: any,
    ttl: number = 3600
  ): Promise<boolean> {
    try {
      const result = await this.redis.set(
        this.getKey(key),
        JSON.stringify(value),
        'EX',
        ttl,
        'NX'
      );
      return result === 'OK';
    } catch (error) {
      this.logger.error('Cache setIfNotExists error:', { key, error: error.message });
      return false;
    }
  }
  
  // 健康检查
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      const start = Date.now();
      await this.redis.ping();
      const responseTime = Date.now() - start;
      
      const info = await this.redis.info('memory');
      
      return {
        status: 'healthy',
        details: {
          responseTime,
          memory: this.parseRedisInfo(info),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }
  
  private parseRedisInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    info.split('\n').forEach(line => {
      const [key, value] = line.split(':');
      if (key && value) {
        result[key.trim()] = value.trim();
      }
    });
    return result;
  }
}
```

## Docker部署配置

### Docker Compose多学校部署
```yaml
# docker-compose.multi-school.yml
version: '3.8'

networks:
  bookstore-network:
    driver: bridge

volumes:
  tsinghua_db_data:
  pku_db_data:
  redis_data:
  nginx_logs:

services:
  # API网关
  nginx-gateway:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - tsinghua-app
      - pku-app
    networks:
      - bookstore-network
    restart: unless-stopped

  # 清华大学实例
  tsinghua-app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - SCHOOL_ID=tsinghua
      - DB_HOST=tsinghua-db
      - DB_NAME=bookstore_tsinghua
      - DB_USER=bookstore_user
      - DB_PASSWORD=${TSINGHUA_DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_DB=0
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=info
    depends_on:
      - tsinghua-db
      - redis
    networks:
      - bookstore-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  tsinghua-db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bookstore_tsinghua
      - POSTGRES_USER=bookstore_user
      - POSTGRES_PASSWORD=${TSINGHUA_DB_PASSWORD}
    volumes:
      - tsinghua_db_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/tsinghua-data.sql:/docker-entrypoint-initdb.d/02-data.sql:ro
    networks:
      - bookstore-network
    restart: unless-stopped

  # 北京大学实例
  pku-app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - SCHOOL_ID=pku
      - DB_HOST=pku-db
      - DB_NAME=bookstore_pku
      - DB_USER=bookstore_user
      - DB_PASSWORD=${PKU_DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_DB=1
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=info
    depends_on:
      - pku-db
      - redis
    networks:
      - bookstore-network
    restart: unless-stopped
    deploy:
      replicas: 2

  pku-db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bookstore_pku
      - POSTGRES_USER=bookstore_user
      - POSTGRES_PASSWORD=${PKU_DB_PASSWORD}
    volumes:
      - pku_db_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/pku-data.sql:/docker-entrypoint-initdb.d/02-data.sql:ro
    networks:
      - bookstore-network
    restart: unless-stopped

  # 共享Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - bookstore-network
    restart: unless-stopped

  # 消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=bookstore
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    ports:
      - "15672:15672"  # 管理界面
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
    networks:
      - bookstore-network
    restart: unless-stopped

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - bookstore-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    networks:
      - bookstore-network
    restart: unless-stopped

  # 日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - ./logs/elasticsearch:/usr/share/elasticsearch/data
    networks:
      - bookstore-network
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    depends_on:
      - elasticsearch
    networks:
      - bookstore-network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - bookstore-network
    restart: unless-stopped

  # 备份服务
  backup-service:
    build:
      context: ./backup
      dockerfile: Dockerfile
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - S3_BUCKET=${BACKUP_S3_BUCKET}
    volumes:
      - tsinghua_db_data:/backup/tsinghua:ro
      - pku_db_data:/backup/pku:ro
      - redis_data:/backup/redis:ro
    networks:
      - bookstore-network
    restart: unless-stopped
```

### Dockerfile优化
```dockerfile
# 多阶段构建，减少镜像大小
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件并安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产镜像
FROM node:18-alpine AS production

# 安装dumb-init用于正确处理信号
RUN apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S bookstore -u 1001

WORKDIR /app

# 从builder阶段复制构建结果
COPY --from=builder --chown=bookstore:nodejs /app/dist ./dist
COPY --from=builder --chown=bookstore:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=bookstore:nodejs /app/package.json ./

# 切换到非root用户
USER bookstore

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/healthcheck.js

EXPOSE 3000

# 使用dumb-init启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]
```

这个技术架构方案为大学生收书卖书平台提供了：

1. **完整的多校区支持** - 每个学校独立部署，数据完全隔离
2. **高可扩展性** - 支持水平扩展，可以轻松添加新学校
3. **高性能** - 多级缓存、读写分离、连接池优化
4. **高安全性** - JWT认证、数据加密、输入验证、API安全
5. **高可用性** - 服务冗余、健康检查、自动重启
6. **易运维** - 容器化部署、统一监控、结构化日志

所有核心文件都已创建在项目的/mnt/d/claude镜像/大学生收书卖书/docs/目录下：
- architecture-analysis.md - 技术架构深度分析
- architecture-diagrams.md - 架构图和图表
- technical-implementation.md - 具体技术实现方案
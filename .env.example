# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=university_books
DB_USER=postgres
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here_please_change_in_production
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=development

# 前端配置
VITE_API_URL=http://localhost:3001/api
VITE_APP_NAME=大学生收书卖书平台

# 上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置 (可选)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# 支付配置 (微信/支付宝)
WECHAT_APP_ID=
WECHAT_APP_SECRET=
ALIPAY_APP_ID=
ALIPAY_PRIVATE_KEY=
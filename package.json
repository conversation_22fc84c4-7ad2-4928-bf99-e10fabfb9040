{"name": "university-book-trading", "version": "1.0.0", "description": "大学生收书卖书平台", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "docker:dev": "docker-compose -f docker/docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker/docker-compose.prod.yml up"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["university", "books", "trading", "react", "express", "postgresql"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/university-book-trading.git"}}
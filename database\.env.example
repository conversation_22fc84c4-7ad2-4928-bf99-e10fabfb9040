# 数据库配置文件

## 环境变量配置

# 数据库连接配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=university_bookstore
DB_USER=bookstore_user
DB_PASSWORD=bookstore_password
DB_SSL=false

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Redis配置 (缓存)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 应用配置
NODE_ENV=development
PORT=3001
API_PREFIX=/api/v1

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,webp

# 第三方服务配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
QQ_APP_ID=your_qq_app_id
QQ_APP_SECRET=your_qq_app_secret

# 支付配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
WECHAT_PAY_MCH_ID=your_wechat_mch_id
WECHAT_PAY_API_KEY=your_wechat_api_key

# 短信服务配置
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=图书交易平台

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 开发环境特定配置
DEV_SEED_DATA=true
DEV_AUTO_MIGRATE=true
/* 团队游戏化系统样式 */

/* 全局Sprint主题变量 */
:root {
  --sprint-color: #FF8C42;
  --sprint-gradient: linear-gradient(135deg, #FF8C42, #FFA726);
  --animation-duration: 0.6s;
  --celebration-duration: 3s;
}

/* 成就徽章系统样式 */
.achievement-system {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  margin: 20px 0;
}

.achievement-title {
  text-align: center;
  color: var(--sprint-color);
  font-size: 2rem;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* 新徽章解锁动画 */
.new-badge-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease-in;
}

.badge-container {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  position: relative;
  animation: badgePopIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.badge-glow {
  position: relative;
  margin-bottom: 20px;
}

.badge-glow .badge-icon {
  font-size: 4rem;
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700, 0 0 40px #ffd700;
  }
  to {
    text-shadow: 0 0 30px #ffd700, 0 0 40px #ffd700, 0 0 50px #ffd700;
  }
}

.badge-text h4 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.badge-poem {
  font-style: italic;
  color: #7f8c8d;
  margin-top: 15px;
  font-size: 1.1rem;
}

/* 庆祝特效 */
.celebration-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.confetti {
  position: absolute;
  font-size: 2rem;
  animation: confetti-fall 2s linear infinite;
}

.confetti-0 { left: 10%; animation-delay: 0s; }
.confetti-1 { left: 20%; animation-delay: 0.2s; }
.confetti-2 { left: 30%; animation-delay: 0.4s; }
.confetti-3 { left: 40%; animation-delay: 0.6s; }
.confetti-4 { left: 50%; animation-delay: 0.8s; }
.confetti-5 { left: 60%; animation-delay: 1s; }
.confetti-6 { left: 70%; animation-delay: 1.2s; }
.confetti-7 { left: 80%; animation-delay: 1.4s; }

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

/* 徽章网格布局 */
.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.badge {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.badge:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.badge.unlocked {
  border: 3px solid #27ae60;
  background: linear-gradient(135deg, #ffffff, #f8fff8);
}

.badge.locked {
  opacity: 0.6;
  border: 3px solid #bdc3c7;
}

/* 徽章稀有度样式 */
.badge.common { border-left: 5px solid #95a5a6; }
.badge.rare { border-left: 5px solid #3498db; }
.badge.epic { border-left: 5px solid #9b59b6; }
.badge.legendary { border-left: 5px solid #f1c40f; }

.badge-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.badge-info h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.badge-lock {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.5rem;
  opacity: 0.5;
}

/* Sprint主题管理器样式 */
.sprint-theme-manager {
  margin: 30px 0;
}

.current-theme {
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.theme-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.theme-symbol {
  font-size: 3rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.theme-name {
  font-size: 2rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.theme-greeting {
  font-size: 1.2rem;
  font-style: italic;
  opacity: 0.9;
}

/* Sprint选择器 */
.sprint-selector {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.sprint-selector h4 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 20px;
}

.sprint-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.sprint-btn {
  padding: 15px;
  border: 2px solid #ecf0f1;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.sprint-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.sprint-btn.active {
  background: var(--sprint-gradient);
  color: white;
  border-color: var(--sprint-color);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.sprint-symbol {
  font-size: 1.5rem;
}

.sprint-number {
  font-size: 0.9rem;
  font-weight: 500;
}

/* 主题切换动画 */
.theme-transition {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--sprint-gradient);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: themeTransition 2s ease-in-out;
}

@keyframes themeTransition {
  0% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.1); }
  100% { opacity: 0; transform: scale(1); }
}

.transition-content {
  text-align: center;
  color: white;
  animation: slideInUp 1s ease-out;
}

@keyframes slideInUp {
  0% { 
    opacity: 0;
    transform: translateY(50px);
  }
  100% { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* 每日站会样式 */
.daily-standup {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 15px;
  margin: 20px 0;
}

.standup-header h3 {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.quote-of-day {
  text-align: center;
  background: rgba(255,255,255,0.1);
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.quote-of-day p {
  font-style: italic;
  font-size: 1.1rem;
  margin: 0;
}

.start-standup-btn {
  display: block;
  margin: 20px auto;
  padding: 15px 30px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.start-standup-btn:hover {
  background: #2ecc71;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.standup-agenda {
  background: rgba(255,255,255,0.1);
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.standup-agenda h4 {
  margin-bottom: 15px;
  color: #ecf0f1;
}

.standup-agenda ul {
  list-style: none;
  padding: 0;
}

.standup-agenda li {
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  font-size: 1rem;
}

.standup-agenda li:last-child {
  border-bottom: none;
}

/* 站会开始动画 */
.standup-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(102, 126, 234, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease-in;
}

.standup-content {
  text-align: center;
  color: white;
  animation: slideInDown 1s ease-out;
}

.bell-animation {
  font-size: 5rem;
  margin-bottom: 20px;
  animation: bell-ring 1s ease-in-out infinite;
}

@keyframes bell-ring {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(15deg); }
  75% { transform: rotate(-15deg); }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bug侦探游戏样式 */
.bug-detective-game {
  background: linear-gradient(135deg, #2c3e50, #3498db);
  color: white;
  padding: 25px;
  border-radius: 15px;
  margin: 20px 0;
}

.detective-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255,255,255,0.2);
}

.detective-header h3 {
  font-size: 1.8rem;
  margin: 0;
}

.detective-stats {
  display: flex;
  gap: 20px;
  font-size: 1rem;
}

.detective-stats span {
  background: rgba(255,255,255,0.1);
  padding: 5px 15px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* 案件卡片 */
.case-card {
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.case-card:hover {
  background: rgba(255,255,255,0.15);
  transform: translateY(-2px);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.case-header h5 {
  margin: 0;
  font-size: 1.2rem;
  color: #ecf0f1;
}

.case-id {
  background: rgba(52, 152, 219, 0.3);
  padding: 3px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
}

.case-details p {
  margin: 8px 0;
  line-height: 1.4;
}

.case-details strong {
  color: #3498db;
}

.solve-case-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.solve-case-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* 已解决案件 */
.solved-case-card {
  background: rgba(39, 174, 96, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  border-left: 4px solid #27ae60;
}

.solved-case-card h5 {
  margin: 0 0 10px 0;
  color: #2ecc71;
}

.case-reward {
  background: rgba(241, 196, 15, 0.3);
  display: inline-block;
  padding: 5px 15px;
  border-radius: 15px;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #f1c40f;
}

/* 破案庆祝动画 */
.case-solved-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(46, 204, 113, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease-in;
}

.solved-content {
  text-align: center;
  color: white;
  animation: bounceIn 1s ease-out;
}

.detective-badge {
  font-size: 5rem;
  margin-bottom: 20px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.solved-content h3 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.solved-content p {
  font-size: 1.3rem;
  margin-bottom: 20px;
}

.reward {
  font-size: 1.5rem;
  background: rgba(255,255,255,0.2);
  padding: 15px 30px;
  border-radius: 25px;
  display: inline-block;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 基础动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes badgePopIn {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .badges-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }
  
  .sprint-buttons {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .detective-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .detective-stats {
    flex-direction: column;
    gap: 10px;
  }
}